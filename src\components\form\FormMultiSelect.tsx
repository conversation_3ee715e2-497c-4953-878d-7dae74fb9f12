import { useFormContext } from 'react-hook-form'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { MultiSelect, MultiSelectProps } from '../ui/multi-select'

interface FormMultiSelectProps<T> extends Omit<MultiSelectProps<T>, 'onChange' | 'onValueChange' | 'onToggle'> {
  data: T[]
  name: string
  valueKey: keyof T
  labelKey: keyof T
  iconKey?: keyof T
  label?: string
  onChange?: (newValue: string[]) => void
  onToggle?: (isOpen: boolean) => void
  required?: boolean
}

export function FormMultiSelect<T>({
  data,
  name,
  label,
  onChange,
  valueKey,
  labelKey,
  iconKey,
  onToggle,
  onSearch,
  required,
  ...props
}: FormMultiSelectProps<T>) {
  const { control } = useFormContext()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label} {required && <span className="text-error-700 dark:text-error-500">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <MultiSelect
              {...field}
              {...props}
              defaultValue={Array.isArray(field.value) ? field.value : []}
              data={data}
              onValueChange={(newValue) => {
                field.onChange(newValue)
                onChange && onChange(newValue)
              }}
              onToggle={(isOpen) => {
                onToggle && onToggle(isOpen)
              }}
              {...(!!onSearch && { onSearch })}
              valueKey={valueKey}
              labelKey={labelKey}
              iconKey={iconKey}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
