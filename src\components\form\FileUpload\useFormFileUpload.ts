import { useState, useRef, type ChangeEvent, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useFormContext } from 'react-hook-form'
import { useFormWrapperContext } from '../../core/FormWrapper'
import { toast } from 'sonner'

type fileTypes =
  | 'image/*'
  | 'video/*'
  | 'audio/*'
  | 'application/pdf'
  | 'application/msword'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.ms-excel'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

export interface FormFileUploadProps {
  label?: string
  name: string
  maxSize?: number // in MB
  maxLength?: number
  accept?: fileTypes
  multiple?: boolean
}

export default function useFormFileUpload({
  label,
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
}: FormFileUploadProps) {
  const t = useTranslations()
  const { errors } = useFormWrapperContext()
  const { control, setValue, getValues, watch } = useFormContext()

  const [isDragging, setIsDragging] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const selectedFiles = watch(name) || []

  const handleFileSelect = (files: FileList) => {
    // Validate max file number
    if (multiple && !validateMaxLength(Array.from(files)))
      return toast.error(t('validations.too_many_files', { maxLength }))

    Array.from(files).forEach((file) => {
      if (!file) return

      // Validate file size
      if (!validateMaxSize(file)) return toast.error(t('validations.file_too_large', { maxSize }))

      setValue(name, multiple ? [...(getValues(name) || []), file] : file)
    })
  }

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (files) {
      handleFileSelect(files)
    }
  }

  const removeImage = (index: number, url?: string) => {
    if (url) URL.revokeObjectURL(url)

    if (fileInputRef.current) fileInputRef.current.value = ''

    // In case input not accepting multiple files
    if (!multiple) return setValue(name, null)

    // In case input accepting multiple files
    const filteredValues = Array.from(selectedFiles).filter((_, i) => i !== index)
    setValue(name, filteredValues)
  }

  const validateMaxLength = (files: File[]): boolean => {
    const currentFiles = getValues(name) || []
    const selectedFilesLength = Array.isArray(currentFiles) ? currentFiles.length : currentFiles ? 1 : 0
    if (maxLength && Array.from(files).length + selectedFilesLength > maxLength) return false

    return true
  }

  const validateMaxSize = (file: File) => {
    if (file.size > maxSize * 1024 * 1024) return false

    return true
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    const files = e.dataTransfer.files
    if (files) {
      handleFileSelect(files)
    }
  }

  function getAllowedFileTypesDescription(): string {
    const mimeToExtensions: Record<fileTypes, string[]> = {
      'image/*': ['*.jpeg', '*.jpg', '*.png', '*.gif', '*.webp'],
      'video/*': ['*.mp4', '*.mov', '*.avi'],
      'audio/*': ['*.mp3', '*.wav', '*.ogg'],
      'application/pdf': ['*.pdf'],
      'application/msword': ['*.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['*.docx'],
      'application/vnd.ms-excel': ['*.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['*.xlsx'],
    }

    return mimeToExtensions[accept].join(', ')
  }

  return {
    selectedFiles,
    handleFileChange,
    removeImage,
    validateMaxLength,
    validateMaxSize,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    getAllowedFileTypesDescription,
    isDragging,
    fileInputRef,
    errors,
    control,
    t,
  }
}
