// hooks
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'

// icons
import { Check, ChevronDownIcon } from 'lucide-react'

// utils
import { cn } from '@/lib/utils'
import { isCountryCode, countryCodeToEmoji } from '@/utils/flagUtils'

// types
import type { ComponentProps } from 'react'

// ui imports
import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CircleFlag } from 'react-circle-flags'

export interface ComboboxProps<T> {
  data: T[]
  valueKey: keyof T
  labelKey: keyof T
  placeholder?: string
  value?: T | null
  onChange?: (value: T | null) => void
  onSearch?: (value: string) => void
  hasError?: boolean
  className?: string
  /** Callback when the popover opens/closes */
  onToggle?: (isOpen: boolean) => void
  onClear?: () => void
}

function Combobox<T>({
  data,
  labelKey,
  valueKey,
  placeholder,
  value: controlledValue,
  onChange,
  onSearch,
  hasError,
  className,
  onToggle,
  onClear,
  ...props
}: ComboboxProps<T> & Omit<ComponentProps<'button'>, keyof ComboboxProps<T> | 'onChange'>) {
  const t = useTranslations()
  const [open, setOpen] = useState(false)
  const [internalValue, setInternalValue] = useState<T | null>(controlledValue ?? null)

  const value = controlledValue !== undefined ? controlledValue : internalValue

  const setValue = (newValue: T | null) => {
    setInternalValue(newValue)
    onChange?.(newValue)
  }

  const handleOpenChange = (next: boolean) => {
    setOpen(next)
    onToggle?.(next)
  }

  useEffect(() => {
    if (controlledValue !== undefined) {
      setInternalValue(controlledValue)
    }
  }, [controlledValue])

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'justify-between bg-bg-input-light dark:bg-bg-input-dark border border-input-border-light dark:border-input-border-dark text-lg text-text-input-light dark:text-text-input-dark font-medium disabled:bg-bg-input-disabled-light disabled:dark:bg-bg-input-disabled-dark',
            hasError && 'border-destructive aria-invalid:border-destructive ',
            className
          )}
          {...props}
        >
          {value ? (
            <span className="flex items-center gap-2">
              {/* Render emoji or flag for selected value */}
              {(value as any)?.iso2 && (
                <div className="flex items-center w-[20px]">
                  {isCountryCode((value as any).iso2) ? (
                    <CircleFlag countryCode={(value as any).iso2.toLowerCase()} height={16} className="rounded-sm" />
                  ) : (
                    <span>{countryCodeToEmoji((value as any).iso2)}</span>
                  )}
                </div>
              )}
              <span>{value[labelKey] as string}</span>
            </span>
          ) : (
            <span className="text-muted-foreground font-normal">{placeholder}</span>
          )}
          <ChevronDownIcon className="opacity-50 ml-2 size-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[var(--radix-popper-anchor-width)] bg-bg-input-light dark:bg-bg-input-dark border border-input-border-light dark:border-input-border-dark text-lg ">
        <Command
          {...(!!onSearch && {
            filter: (_, search) => {
              onSearch(search)
              return 1
            },
          })}
        >
          <CommandInput placeholder={t('label.search')} className="h-9" />
          <CommandList>
            <CommandEmpty>{t('label.no_data')}</CommandEmpty>
            <CommandGroup>
              {data.map((item) => {
                const itemValue = String(item[valueKey])
                const itemLabel = String(item[labelKey])
                const isSelected = value && String(value[valueKey]) === itemValue

                return (
                  <CommandItem
                    key={itemValue}
                    value={itemLabel}
                    className="capitalize"
                    onSelect={() => {
                      const selectedItem = item
                      const isSame = value && String(value[valueKey]) === String(selectedItem[valueKey])
                      isSame ? (onClear?.(), setValue(null)) : setValue(selectedItem)
                      setOpen(false)
                    }}
                  >
                    <span className="flex items-center gap-2">
                      {/* Render emoji or flag */}
                      {(item as any)?.iso2 && (
                        <span className="flex items-center w-[20px]">
                          {isCountryCode((item as any).iso2) ? (
                            <CircleFlag
                              countryCode={(item as any).iso2.toLowerCase()}
                              height={16}
                              className="rounded-sm"
                            />
                          ) : (
                            <span>{countryCodeToEmoji((item as any).iso2)}</span>
                          )}
                        </span>
                      )}
                      <span>{itemLabel}</span>
                    </span>
                    <Check className={cn('ml-auto size-4', isSelected ? 'opacity-100' : 'opacity-0')} />
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

export { Combobox }
